#include<iostream>
#include<string.h>
#include<vector>
#include<cstdlib>
#include<ctime>
#include<thread>
#include <fstream>
#include <mutex>

using namespace std;
class Student
{
    float score;
public:
    int ID;
    string name;
    string major;

    Student();
    Student(string name, float score)
{
    this->name = name;
    this->score = score;
}
    ~Student()
    {
        //cout << "Student " << name << " is graduated" << endl;
    }

    void display(void) const
    {
        cout << "Name: "<< name << ", Score: "<< score << endl;
    }
    float getScore() const{
        return score;
    }
};

void calculateAverageCPA(const vector<Student> &studentList)
{
    float totalScore = 0.0;
    for(const auto& student: studentList){
        totalScore +=student.getScore();
    }
    float averageCPA = totalScore / studentList.size();
    cout << "Average CPA: "<< averageCPA<< endl;
}

void filterAndWriteToFile(const vector<Student> &studentList)
{
    ofstream outFile("honor.txt", ios::out);
    if(!outFile){
        cerr << "Open honor file fail"<< endl;
    }
    for(const auto&student : studentList)
    {
        if(student.getScore() >= 3.2){
            outFile << "Name: " << student.name << ", Score: "<< student.getScore()<< endl;
        }
    }

    outFile.close();
    cout << "Sudents with GPA >= 3.2 is recieved the honor "<< endl;

}


int main(void)
{
    srand(time(0));
    vector<Student> studentList; 
    for(int i = 1; i <= 100; i++)
    {
        string name = "SV" + (i<10?"0"+ to_string(i): to_string(i));
        float score = static_cast<float>(rand()) / RAND_MAX * 4.0;
        studentList.emplace_back(name, score);
    }

    for(const auto &student: studentList)
    {
        student.display();
    }

    thread thread1(calculateAverageCPA, ref(studentList));
    thread1.join();

    thread thread2(filterAndWriteToFile, cref(studentList));
    thread2.join();
    return 0;
}