#include<iostream>
#include<string>
#include<vector>
#include<cstdlib>
#include<ctime>
#include<thread>
#include<fstream>
#include<mutex>
#include<random>

using namespace std;

class Student
{
private:
    int gpa; // GPA là số nguyên từ 0-10
public:
    int ID;
    string name;

    Student() {}
    Student(int id, string name, int gpa)
    {
        this->ID = id;
        this->name = name;
        this->gpa = gpa;
    }

    ~Student()
    {
        // Destructor
    }

    void display() const
    {
        cout << "ID: " << ID << ", Name: " << name << ", GPA: " << gpa << endl;
    }

    int getGPA() const {
        return gpa;
    }
};

// Biến toàn cục để lưu kết quả
double averageGPA = 0.0;
int honorsStudentCount = 0;
mutex resultMutex; // Mutex để đồng bộ truy cập kết quả

// Thread 1: Tính trung bình GPA
void calculateAverageGPA(const vector<Student> &studentList)
{
    double totalGPA = 0.0;
    for(const auto& student: studentList){
        totalGPA += student.getGPA();
    }

    lock_guard<mutex> lock(resultMutex);
    averageGPA = totalGPA / studentList.size();
    cout << "Thread 1: Da tinh xong trung binh GPA" << endl;
}

// Thread 2: Lọc sinh viên có GPA > 5 và ghi vào file
void filterAndWriteToFile(const vector<Student> &studentList)
{
    ofstream outFile("honors.txt", ios::out);
    if(!outFile){
        cerr << "Loi: Khong the mo file honors.txt" << endl;
        return;
    }

    int count = 0;
    for(const auto& student : studentList)
    {
        if(student.getGPA() > 5){
            outFile << "ID: " << student.ID << ", Name: " << student.name
                   << ", GPA: " << student.getGPA() << endl;
            count++;
        }
    }

    outFile.close();

    lock_guard<mutex> lock(resultMutex);
    honorsStudentCount = count;
    cout << "Thread 2: Da ghi " << count << " sinh vien vao file honors.txt" << endl;
}


// Hàm tạo tên ngẫu nhiên tối đa 10 ký tự
string generateRandomName() {
    const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    string name = "";
    int length = rand() % 8 + 3; // Độ dài từ 3-10 ký tự
    for(int i = 0; i < length; i++) {
        name += chars[rand() % chars.length()];
    }
    return name;
}

int main()
{
    srand(time(0));

    cout << "Dang tao danh sach 100,000 sinh vien..." << endl;
    vector<Student> studentList;
    studentList.reserve(100000); // Du tru bo nho de tang hieu suat

    // Tao 100,000 sinh vien
    for(int i = 0; i < 100000; i++)
    {
        int id = rand() % 1000000000 + 1; // ID tu 1 den 1 ty
        string name = generateRandomName(); // Ten ngau nhien toi da 10 ky tu
        int gpa = rand() % 11; // GPA tu 0-10
        studentList.emplace_back(id, name, gpa);
    }

    cout << "Da tao xong " << studentList.size() << " sinh vien." << endl;
    cout << "Bat dau xu ly voi 2 thread..." << endl;

    // Tạo 2 thread để xử lý
    thread thread1(calculateAverageGPA, cref(studentList));
    thread thread2(filterAndWriteToFile, cref(studentList));

    // Chờ cả 2 thread hoàn thành
    thread1.join();
    thread2.join();

    // In ket qua
    cout << "\n=== KET QUA ===" << endl;
    cout << "Trung binh GPA toan bo sinh vien: " << averageGPA << endl;
    cout << "So sinh vien dat danh hieu (GPA > 5): " << honorsStudentCount << endl;
    cout << "Danh sach sinh vien dat danh hieu da duoc ghi vao file honors.txt" << endl;

    return 0;
}