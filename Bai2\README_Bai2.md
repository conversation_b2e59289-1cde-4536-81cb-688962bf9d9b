# Câu 2 - <PERSON><PERSON><PERSON> tiến trình tra cứu sinh viên

## <PERSON><PERSON> tả
Chương trình gồm 2 phiên bản:
1. **<PERSON><PERSON><PERSON> bản cơ bản**: <PERSON><PERSON><PERSON><PERSON><PERSON> tham số qua command line
2. **<PERSON><PERSON><PERSON> bản nâng cao**: <PERSON><PERSON> dụng shared memory

## Files trong dự án

### D<PERSON> liệu
- `students.txt` - File chứa danh sách sinh viên (ID, Tên, GPA)

### Phiên bản c<PERSON> bản (Command Line)
- `main_process.cpp` - Chương trình chính
- `main_process.exe` - File thực thi chương trình chính
- `student_lookup.cpp` - Chương trình tra cứu sinh viên
- `student_lookup.exe` - File thực thi tra cứu

### Phiên bản nâng cao (Shared Memory)
- `main_process_shared.cpp` - Chương trình chính sử dụng shared memory
- `main_process_shared.exe` - File thực thi chương trình chính (shared memory)
- `student_lookup_shared.cpp` - <PERSON><PERSON><PERSON>ng trình tra cứu sử dụng shared memory
- `student_lookup_shared.exe` - File thực thi tra cứu (shared memory)

## Cách sử dụng

### Phiên bản cơ bản
```bash
# Chạy chương trình chính
./main_process.exe

# Hoặc test trực tiếp chương trình tra cứu
./student_lookup.exe 123456789
```

### Phiên bản shared memory
```bash
# Chạy chương trình chính với shared memory
./main_process_shared.exe
```

## Danh sách ID sinh viên có sẵn
- 123456789 - NGUYEN VAN A (GPA: 8)
- 987654321 - TRAN THI B (GPA: 7)
- 456789123 - LE VAN C (GPA: 9)
- 789123456 - PHAM THI D (GPA: 6)
- 321654987 - HOANG VAN E (GPA: 5)
- 654987321 - VUONG THI F (GPA: 10)
- 147258369 - DANG VAN G (GPA: 4)
- 963852741 - BUI THI H (GPA: 8)
- 258147963 - NGO VAN I (GPA: 7)
- 741852963 - DO THI J (GPA: 9)

## Tính năng đã thực hiện

### ✅ Yêu cầu cơ bản
- [x] Cho phép người dùng nhập ID sinh viên
- [x] Sử dụng CreateProcess để tạo tiến trình con
- [x] Truyền ID qua command line arguments
- [x] Đọc file students.txt để tra cứu
- [x] In thông tin sinh viên nếu tìm thấy
- [x] Thông báo "Không tìm thấy" nếu không có
- [x] Sử dụng WaitForSingleObject để chờ tiến trình con

### ✅ Tính năng nâng cao
- [x] Sử dụng shared memory để truyền dữ liệu
- [x] Đồng bộ hóa giữa các tiến trình
- [x] Xử lý lỗi và cleanup tài nguyên
- [x] Giao diện người dùng thân thiện
- [x] Hỗ trợ tra cứu nhiều lần

## Cách biên dịch
```bash
# Biên dịch phiên bản cơ bản
g++ -o main_process.exe main_process.cpp
g++ -o student_lookup.exe student_lookup.cpp

# Biên dịch phiên bản shared memory
g++ -o main_process_shared.exe main_process_shared.cpp
g++ -o student_lookup_shared.exe student_lookup_shared.cpp
```

## Kiến trúc hệ thống

### Phiên bản cơ bản
```
main_process.exe
    ↓ CreateProcess
    ↓ Command line: "student_lookup.exe <ID>"
student_lookup.exe
    ↓ Đọc argv[1]
    ↓ Tra cứu trong students.txt
    ↓ In kết quả ra console
```

### Phiên bản shared memory
```
main_process_shared.exe
    ↓ Tạo shared memory
    ↓ Ghi ID vào shared memory
    ↓ CreateProcess
student_lookup_shared.exe
    ↓ Đọc ID từ shared memory
    ↓ Tra cứu trong students.txt
    ↓ Ghi kết quả vào shared memory
    ↓ Đánh dấu ready = true
main_process_shared.exe
    ↓ Đọc kết quả từ shared memory
    ↓ Hiển thị cho người dùng
```
