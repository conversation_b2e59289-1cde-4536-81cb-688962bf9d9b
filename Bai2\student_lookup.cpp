#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <windows.h>

using namespace std;

struct Student {
    string id;
    string name;
    int gpa;
    
    Student() {}
    Student(string id, string name, int gpa) : id(id), name(name), gpa(gpa) {}
};

// Ham tim sinh vien theo ID trong file
bool findStudentByID(const string& studentID, Student& foundStudent) {
    ifstream file("students.txt");
    if (!file.is_open()) {
        cerr << "Loi: Khong the mo file students.txt" << endl;
        return false;
    }
    
    string line;
    while (getline(file, line)) {
        stringstream ss(line);
        string id, name, gpaStr;
        
        // Doc theo format: ID,NAME,GPA
        if (getline(ss, id, ',') && 
            getline(ss, name, ',') && 
            getline(ss, gpaStr)) {
            
            if (id == studentID) {
                foundStudent = Student(id, name, stoi(gpaStr));
                file.close();
                return true;
            }
        }
    }
    
    file.close();
    return false;
}

int main(int argc, char* argv[]) {
    // Kiem tra tham so dau vao
    if (argc != 2) {
        cerr << "Su dung: student_lookup.exe <student_id>" << endl;
        return 1;
    }
    
    string studentID = argv[1];
    cout << "Dang tim kiem sinh vien co ID: " << studentID << endl;
    
    Student foundStudent;
    if (findStudentByID(studentID, foundStudent)) {
        cout << "\n=== THONG TIN SINH VIEN ===" << endl;
        cout << "ID: " << foundStudent.id << endl;
        cout << "Ten: " << foundStudent.name << endl;
        cout << "GPA: " << foundStudent.gpa << endl;
        cout << "=========================" << endl;
    } else {
        cout << "\nKhong tim thay sinh vien co ID: " << studentID << endl;
    }
    
    return 0;
}
