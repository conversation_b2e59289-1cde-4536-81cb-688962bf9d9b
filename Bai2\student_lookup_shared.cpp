#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <windows.h>

using namespace std;

// Cau truc du lieu chia se qua shared memory (phai giong voi main_process_shared.cpp)
struct SharedData {
    char studentID[20];
    char result[500];
    bool found;
    bool ready;
};

struct Student {
    string id;
    string name;
    int gpa;
    
    Student() {}
    Student(string id, string name, int gpa) : id(id), name(name), gpa(gpa) {}
};

// Ham tim sinh vien theo ID trong file
bool findStudentByID(const string& studentID, Student& foundStudent) {
    ifstream file("students.txt");
    if (!file.is_open()) {
        cerr << "Loi: Khong the mo file students.txt" << endl;
        return false;
    }
    
    string line;
    while (getline(file, line)) {
        stringstream ss(line);
        string id, name, gpaStr;
        
        // Doc theo format: ID,NAME,GPA
        if (getline(ss, id, ',') && 
            getline(ss, name, ',') && 
            getline(ss, gpaStr)) {
            
            if (id == studentID) {
                foundStudent = Student(id, name, stoi(gpaStr));
                file.close();
                return true;
            }
        }
    }
    
    file.close();
    return false;
}

int main() {
    cout << "Student lookup process (shared memory) started..." << endl;
    
    // Mo shared memory
    HANDLE hMapFile = OpenFileMapping(
        FILE_MAP_ALL_ACCESS,   // Read/write access
        FALSE,                 // Do not inherit the name
        TEXT("StudentLookupSharedMemory") // Name of mapping object
    );
    
    if (hMapFile == NULL) {
        cerr << "Loi: Khong the mo shared memory. Ma loi: " << GetLastError() << endl;
        return 1;
    }
    
    // Map shared memory vao address space
    SharedData* pSharedData = (SharedData*)MapViewOfFile(
        hMapFile,               // Handle to map object
        FILE_MAP_ALL_ACCESS,    // Read/write permission
        0,
        0,
        sizeof(SharedData)
    );
    
    if (pSharedData == NULL) {
        cerr << "Loi: Khong the map shared memory. Ma loi: " << GetLastError() << endl;
        CloseHandle(hMapFile);
        return 1;
    }
    
    // Doc student ID tu shared memory
    string studentID = pSharedData->studentID;
    cout << "Dang tim kiem sinh vien co ID: " << studentID << endl;
    
    Student foundStudent;
    if (findStudentByID(studentID, foundStudent)) {
        // Ghi ket qua vao shared memory
        pSharedData->found = true;
        
        string result = "=== THONG TIN SINH VIEN ===\n";
        result += "ID: " + foundStudent.id + "\n";
        result += "Ten: " + foundStudent.name + "\n";
        result += "GPA: " + to_string(foundStudent.gpa) + "\n";
        result += "===========================";
        
        strcpy(pSharedData->result, result.c_str());
        
        cout << "Tim thay sinh vien. Da ghi ket qua vao shared memory." << endl;
    } else {
        pSharedData->found = false;
        strcpy(pSharedData->result, "");
        cout << "Khong tim thay sinh vien. Da cap nhat shared memory." << endl;
    }
    
    // Danh dau la da san sang
    pSharedData->ready = true;
    
    // Cleanup
    UnmapViewOfFile(pSharedData);
    CloseHandle(hMapFile);
    
    cout << "Student lookup process hoan thanh." << endl;
    return 0;
}
