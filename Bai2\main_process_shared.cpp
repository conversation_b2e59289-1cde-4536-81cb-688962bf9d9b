#include <iostream>
#include <string>
#include <windows.h>

using namespace std;

// Cau truc du lieu chia se qua shared memory
struct SharedData {
    char studentID[20];
    char result[500];
    bool found;
    bool ready;
};

// Ham tao tien trinh con bang CreateProcess voi shared memory
bool createChildProcessWithSharedMemory(const string& studentID) {
    // Tao shared memory
    HANDLE hMapFile = CreateFileMapping(
        INVALID_HANDLE_VALUE,    // Su dung paging file
        NULL,                    // Security attributes
        PAGE_READWRITE,          // Read/write access
        0,                       // Maximum object size (high-order DWORD)
        sizeof(SharedData),      // Maximum object size (low-order DWORD)
        TEXT("StudentLookupSharedMemory") // Name of mapping object
    );
    
    if (hMapFile == NULL) {
        cerr << "Loi: Khong the tao shared memory. Ma loi: " << GetLastError() << endl;
        return false;
    }
    
    // Map shared memory vao address space
    SharedData* pSharedData = (SharedData*)MapViewOfFile(
        hMapFile,               // Handle to map object
        FILE_MAP_ALL_ACCESS,    // Read/write permission
        0,
        0,
        sizeof(SharedData)
    );
    
    if (pSharedData == NULL) {
        cerr << "Loi: Khong the map shared memory. Ma loi: " << GetLastError() << endl;
        CloseHandle(hMapFile);
        return false;
    }
    
    // Khoi tao du lieu trong shared memory
    strcpy(pSharedData->studentID, studentID.c_str());
    pSharedData->found = false;
    pSharedData->ready = false;
    strcpy(pSharedData->result, "");
    
    STARTUPINFO si;
    PROCESS_INFORMATION pi;
    
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    ZeroMemory(&pi, sizeof(pi));
    
    // Tao chuoi lenh de chay tien trinh con
    string commandLine = "student_lookup_shared.exe";
    char* cmdLine = new char[commandLine.length() + 1];
    strcpy(cmdLine, commandLine.c_str());
    
    cout << "Dang tao tien trinh con voi shared memory..." << endl;
    
    // Tao tien trinh con
    BOOL success = CreateProcess(
        NULL, cmdLine, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi
    );
    
    if (!success) {
        cerr << "Loi: Khong the tao tien trinh con. Ma loi: " << GetLastError() << endl;
        UnmapViewOfFile(pSharedData);
        CloseHandle(hMapFile);
        delete[] cmdLine;
        return false;
    }
    
    cout << "Tien trinh con da duoc tao. Process ID: " << pi.dwProcessId << endl;
    cout << "Dang cho ket qua tu shared memory..." << endl;
    
    // Cho tien trinh con hoan thanh
    WaitForSingleObject(pi.hProcess, INFINITE);
    
    // Doc ket qua tu shared memory
    if (pSharedData->ready) {
        cout << "\n=== KET QUA TU SHARED MEMORY ===" << endl;
        if (pSharedData->found) {
            cout << pSharedData->result << endl;
        } else {
            cout << "Khong tim thay sinh vien co ID: " << studentID << endl;
        }
        cout << "===============================" << endl;
    } else {
        cout << "Loi: Khong nhan duoc ket qua tu tien trinh con." << endl;
    }
    
    // Cleanup
    UnmapViewOfFile(pSharedData);
    CloseHandle(hMapFile);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    delete[] cmdLine;
    
    return true;
}

int main() {
    cout << "=== CHUONG TRINH TRA CUU SINH VIEN (SHARED MEMORY) ===" << endl;
    cout << "Danh sach ID sinh vien co san:" << endl;
    cout << "123456789, 987654321, 456789123, 789123456, 321654987" << endl;
    cout << "654987321, 147258369, 963852741, 258147963, 741852963" << endl;
    cout << "========================================================" << endl;
    
    string studentID;
    
    while (true) {
        cout << "\nNhap ID sinh vien can tra cuu (hoac 'exit' de thoat): ";
        getline(cin, studentID);
        
        if (studentID == "exit" || studentID == "EXIT") {
            cout << "Cam on ban da su dung chuong trinh!" << endl;
            break;
        }
        
        if (studentID.empty()) {
            cout << "Vui long nhap ID hop le!" << endl;
            continue;
        }
        
        cout << "\n--- BAT DAU TRA CUU (SHARED MEMORY) ---" << endl;
        
        if (!createChildProcessWithSharedMemory(studentID)) {
            cout << "Khong the thuc hien tra cuu." << endl;
        }
        
        cout << "--- KET THUC TRA CUU ---" << endl;
    }
    
    return 0;
}
