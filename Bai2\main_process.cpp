#include <iostream>
#include <string>
#include <windows.h>

using namespace std;

// Ham tao tien trinh con bang CreateProcess
bool createChildProcess(const string& studentID) {
    STARTUPINFO si;
    PROCESS_INFORMATION pi;
    
    // Khoi tao cau truc
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    ZeroMemory(&pi, sizeof(pi));
    
    // Tao chuoi lenh de chay tien trinh con
    string commandLine = "student_lookup.exe " + studentID;
    
    // Chuyen string thanh LPSTR (can thiet cho CreateProcess)
    char* cmdLine = new char[commandLine.length() + 1];
    strcpy(cmdLine, commandLine.c_str());
    
    cout << "Dang tao tien trinh con voi lenh: " << commandLine << endl;
    
    // Tao tien trinh con
    BOOL success = CreateProcess(
        NULL,           // Ten ung dung (NULL = su dung command line)
        cmdLine,        // <PERSON> lenh
        NULL,           // Process security attributes
        NULL,           // Thread security attributes
        FALSE,          // Inherit handles
        0,              // Creation flags
        NULL,           // Environment
        NULL,           // Current directory
        &si,            // Startup info
        &pi             // Process info
    );
    
    if (!success) {
        cerr << "Loi: Khong the tao tien trinh con. Ma loi: " << GetLastError() << endl;
        delete[] cmdLine;
        return false;
    }
    
    cout << "Tien trinh con da duoc tao thanh cong. Process ID: " << pi.dwProcessId << endl;
    cout << "Dang cho tien trinh con hoan thanh..." << endl;
    
    // Cho tien trinh con hoan thanh
    DWORD waitResult = WaitForSingleObject(pi.hProcess, INFINITE);
    
    if (waitResult == WAIT_OBJECT_0) {
        cout << "Tien trinh con da hoan thanh." << endl;
        
        // Lay ma thoat cua tien trinh con
        DWORD exitCode;
        if (GetExitCodeProcess(pi.hProcess, &exitCode)) {
            cout << "Ma thoat cua tien trinh con: " << exitCode << endl;
        }
    } else {
        cerr << "Loi khi cho tien trinh con. Ma loi: " << GetLastError() << endl;
    }
    
    // Dong handle
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);
    delete[] cmdLine;
    
    return true;
}

int main() {
    cout << "=== CHUONG TRINH TRA CUU SINH VIEN ===" << endl;
    cout << "Danh sach ID sinh vien co san:" << endl;
    cout << "123456789, 987654321, 456789123, 789123456, 321654987" << endl;
    cout << "654987321, 147258369, 963852741, 258147963, 741852963" << endl;
    cout << "=======================================" << endl;
    
    string studentID;
    
    while (true) {
        cout << "\nNhap ID sinh vien can tra cuu (hoac 'exit' de thoat): ";
        getline(cin, studentID);
        
        if (studentID == "exit" || studentID == "EXIT") {
            cout << "Cam on ban da su dung chuong trinh!" << endl;
            break;
        }
        
        if (studentID.empty()) {
            cout << "Vui long nhap ID hop le!" << endl;
            continue;
        }
        
        cout << "\n--- BAT DAU TRA CUU ---" << endl;
        
        // Tao tien trinh con de tra cuu
        if (!createChildProcess(studentID)) {
            cout << "Khong the thuc hien tra cuu." << endl;
        }
        
        cout << "--- KET THUC TRA CUU ---" << endl;
    }
    
    return 0;
}
